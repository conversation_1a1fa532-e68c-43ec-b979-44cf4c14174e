namespace UNI.Utilities.ExternalStorage.Models
{
    /// <summary>
    /// Configuration settings for MinIO storage provider
    /// </summary>
    public class MinIOStorageSettings : StorageSettings
    {
        /// <summary>
        /// Storage provider type
        /// </summary>
        public override StorageProviderType ProviderType => StorageProviderType.MinIO;

        /// <summary>
        /// MinIO server endpoint (internal/direct endpoint)
        /// </summary>
        public string Endpoint { get; set; } = string.Empty;

        /// <summary>
        /// Proxy endpoint for public access (optional)
        /// This is used for generating presigned URLs that are accessible from outside
        /// </summary>
        public string? ProxyEndpoint { get; set; }

        /// <summary>
        /// Access key for authentication
        /// </summary>
        public string AccessKey { get; set; } = string.Empty;

        /// <summary>
        /// Secret key for authentication
        /// </summary>
        public string SecretKey { get; set; } = string.Empty;

        /// <summary>
        /// Whether to use SSL/TLS
        /// </summary>
        public bool UseSSL { get; set; } = true;

        /// <summary>
        /// Region for the MinIO server (optional)
        /// </summary>
        public string? Region { get; set; }
    }

    /// <summary>
    /// Configuration settings for AWS S3 storage provider
    /// </summary>
    public class AwsS3StorageSettings : StorageSettings
    {
        /// <summary>
        /// Storage provider type
        /// </summary>
        public override StorageProviderType ProviderType => StorageProviderType.AwsS3;

        /// <summary>
        /// AWS access key ID
        /// </summary>
        public string AccessKeyId { get; set; } = string.Empty;

        /// <summary>
        /// AWS secret access key
        /// </summary>
        public string SecretAccessKey { get; set; } = string.Empty;

        /// <summary>
        /// AWS session token (for temporary credentials)
        /// </summary>
        public string? SessionToken { get; set; }

        /// <summary>
        /// AWS region
        /// </summary>
        public string Region { get; set; } = "us-east-1";

        /// <summary>
        /// Custom S3 service URL (for S3-compatible services)
        /// </summary>
        public string? ServiceUrl { get; set; }

        /// <summary>
        /// Whether to force path style addressing
        /// </summary>
        public bool ForcePathStyle { get; set; } = false;

        /// <summary>
        /// Whether to use accelerate endpoint
        /// </summary>
        public bool UseAccelerateEndpoint { get; set; } = false;

        /// <summary>
        /// Whether to use dual stack endpoint (IPv6)
        /// </summary>
        public bool UseDualstackEndpoint { get; set; } = false;

        /// <summary>
        /// Storage class for uploaded objects
        /// </summary>
        public string? StorageClass { get; set; }

        /// <summary>
        /// Server-side encryption method
        /// </summary>
        public string? ServerSideEncryption { get; set; }

        /// <summary>
        /// KMS key ID for server-side encryption
        /// </summary>
        public string? KmsKeyId { get; set; }
    }

    /// <summary>
    /// Configuration settings for Firebase storage provider
    /// </summary>
    public class FirebaseStorageSettings : StorageSettings
    {
        /// <summary>
        /// Storage provider type
        /// </summary>
        public override StorageProviderType ProviderType => StorageProviderType.Firebase;

        /// <summary>
        /// Firebase project ID
        /// </summary>
        public string ProjectId { get; set; } = string.Empty;

        /// <summary>
        /// Path to Firebase service account key file
        /// </summary>
        public string? ServiceAccountKeyPath { get; set; }

        /// <summary>
        /// Firebase service account key JSON content
        /// </summary>
        public string? ServiceAccountKeyJson { get; set; }

        /// <summary>
        /// Firebase storage bucket name
        /// </summary>
        public string? StorageBucket { get; set; }

        /// <summary>
        /// Firebase app name (for multiple Firebase apps)
        /// </summary>
        public string? AppName { get; set; }

        /// <summary>
        /// Whether to use Firebase emulator
        /// </summary>
        public bool UseEmulator { get; set; } = false;

        /// <summary>
        /// Firebase emulator host (when using emulator)
        /// </summary>
        public string? EmulatorHost { get; set; }

        /// <summary>
        /// Firebase emulator port (when using emulator)
        /// </summary>
        public int? EmulatorPort { get; set; }

        /// <summary>
        /// Default download URL domain
        /// </summary>
        public string? DownloadUrlDomain { get; set; }
    }

    /// <summary>
    /// Configuration settings for Azure Blob storage provider
    /// </summary>
    public class AzureBlobStorageSettings : StorageSettings
    {
        /// <summary>
        /// Storage provider type
        /// </summary>
        public override StorageProviderType ProviderType => StorageProviderType.AzureBlob;

        /// <summary>
        /// Azure storage account connection string
        /// </summary>
        public string ConnectionString { get; set; } = string.Empty;

        /// <summary>
        /// Azure storage account name
        /// </summary>
        public string? AccountName { get; set; }

        /// <summary>
        /// Azure storage account key
        /// </summary>
        public string? AccountKey { get; set; }

        /// <summary>
        /// Azure storage SAS token
        /// </summary>
        public string? SasToken { get; set; }

        /// <summary>
        /// Azure storage endpoint
        /// </summary>
        public string? Endpoint { get; set; }

        /// <summary>
        /// Whether to use HTTPS
        /// </summary>
        public bool UseHttps { get; set; } = true;

        /// <summary>
        /// Default blob access tier
        /// </summary>
        public string? DefaultAccessTier { get; set; }
    }

    /// <summary>
    /// Configuration settings for Google Cloud Storage provider
    /// </summary>
    public class GoogleCloudStorageSettings : StorageSettings
    {
        /// <summary>
        /// Storage provider type
        /// </summary>
        public override StorageProviderType ProviderType => StorageProviderType.GoogleCloud;

        /// <summary>
        /// Google Cloud project ID
        /// </summary>
        public string ProjectId { get; set; } = string.Empty;

        /// <summary>
        /// Path to Google Cloud service account key file
        /// </summary>
        public string? ServiceAccountKeyPath { get; set; }

        /// <summary>
        /// Google Cloud service account key JSON content
        /// </summary>
        public string? ServiceAccountKeyJson { get; set; }

        /// <summary>
        /// Google Cloud Storage endpoint (for custom endpoints)
        /// </summary>
        public string? Endpoint { get; set; }

        /// <summary>
        /// Default storage class for uploaded objects
        /// </summary>
        public string? StorageClass { get; set; }

        /// <summary>
        /// Default location for buckets
        /// </summary>
        public string? DefaultLocation { get; set; }
    }
}
