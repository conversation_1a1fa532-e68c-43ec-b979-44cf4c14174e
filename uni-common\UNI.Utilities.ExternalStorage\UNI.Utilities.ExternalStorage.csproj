<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <GeneratePackageOnBuild>false</GeneratePackageOnBuild>
    <Authors>UNI Cloud Group</Authors>
    <Company>UNI Cloud Group</Company>
    <Product>UNI Utilities External Storage</Product>
    <Description>A comprehensive .NET library for external storage operations with support for multiple providers including MinIO, AWS S3, Firebase Storage, and more.</Description>
    <PackageTags>storage;minio;aws;s3;firebase;cloud;object-storage</PackageTags>
    <RepositoryType>git</RepositoryType>
    <PackageLicenseExpression>MIT</PackageLicenseExpression>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.7" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.7" />
    <PackageReference Include="Microsoft.Extensions.Options" Version="9.0.7" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.4" />
    <PackageReference Include="AWSSDK.S3" Version="3.7.307.25" />
    <PackageReference Include="FirebaseAdmin" Version="2.4.0" />
    <PackageReference Include="Google.Cloud.Storage.V1" Version="4.6.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\UNI.Utilities.MinIo\UNI.Utilities.MinIo.csproj" />
  </ItemGroup>

</Project>
