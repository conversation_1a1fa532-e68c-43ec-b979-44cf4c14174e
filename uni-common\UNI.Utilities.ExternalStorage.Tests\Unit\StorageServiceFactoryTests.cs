using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using UNI.Utilities.ExternalStorage.Abstractions;
using UNI.Utilities.ExternalStorage.Exceptions;
using UNI.Utilities.ExternalStorage.Models;
using UNI.Utilities.ExternalStorage.Providers;
using UNI.Utilities.ExternalStorage.Services;
using Xunit;

namespace UNI.Utilities.ExternalStorage.Tests.Unit
{
    public class StorageServiceFactoryTests
    {
        private readonly Mock<IServiceProvider> _mockServiceProvider;
        private readonly Mock<ILogger<StorageServiceFactory>> _mockLogger;
        private readonly Mock<IStorageProviderRegistry> _mockProviderRegistry;
        private readonly ExternalStorageConfiguration _configuration;

        public StorageServiceFactoryTests()
        {
            _mockServiceProvider = new Mock<IServiceProvider>();
            _mockLogger = new Mock<ILogger<StorageServiceFactory>>();
            _mockProviderRegistry = new Mock<IStorageProviderRegistry>();

            _configuration = new ExternalStorageConfiguration
            {
                DefaultProvider = StorageProviderType.MinIO,
                MinIO = new MinIOStorageSettings
                {
                    Endpoint = "localhost:9000",
                    AccessKey = "test-access-key",
                    SecretKey = "test-secret-key",
                    UseSSL = false
                },
                AwsS3 = new AwsS3StorageSettings
                {
                    AccessKeyId = "test-access-key-id",
                    SecretAccessKey = "test-secret-access-key",
                    Region = "us-east-1"
                },
                Firebase = new FirebaseStorageSettings
                {
                    ProjectId = "test-project-id",
                    ServiceAccountKeyJson = "{\"type\":\"service_account\"}"
                }
            };

            // Setup service provider to return loggers
            _mockServiceProvider.Setup(x => x.GetService(typeof(ILogger<MinIOStorageProvider>)))
                .Returns(new Mock<ILogger<MinIOStorageProvider>>().Object);
            _mockServiceProvider.Setup(x => x.GetService(typeof(ILogger<AwsS3StorageProvider>)))
                .Returns(new Mock<ILogger<AwsS3StorageProvider>>().Object);
            // Firebase provider disabled
            // _mockServiceProvider.Setup(x => x.GetService(typeof(ILogger<FirebaseStorageProvider>)))
            //     .Returns(new Mock<ILogger<FirebaseStorageProvider>>().Object);
        }

        [Fact]
        public void Constructor_WithValidParameters_ShouldCreateInstance()
        {
            // Arrange
            var options = Options.Create(_configuration);

            // Act
            var factory = new StorageServiceFactory(options, _mockServiceProvider.Object, _mockLogger.Object, _mockProviderRegistry.Object);

            // Assert
            Assert.NotNull(factory);
        }

        [Fact]
        public void Constructor_WithNullConfiguration_ShouldThrowArgumentNullException()
        {
            // Arrange
            IOptions<ExternalStorageConfiguration> nullOptions = null!;

            // Act & Assert
            Assert.Throws<ArgumentNullException>(() =>
                new StorageServiceFactory(nullOptions, _mockServiceProvider.Object, _mockLogger.Object, _mockProviderRegistry.Object));
        }

        [Fact]
        public void CreateDefaultStorageService_ShouldReturnMinIOProvider()
        {
            // Arrange
            var options = Options.Create(_configuration);
            var factory = new StorageServiceFactory(options, _mockServiceProvider.Object, _mockLogger.Object, _mockProviderRegistry.Object);

            // Act
            var storageService = factory.CreateDefaultStorageService();

            // Assert
            Assert.NotNull(storageService);
            Assert.Equal(StorageProviderType.MinIO, storageService.ProviderType);
        }

        [Fact]
        public void CreateStorageService_WithMinIOProvider_ShouldReturnMinIOProvider()
        {
            // Arrange
            var options = Options.Create(_configuration);
            var factory = new StorageServiceFactory(options, _mockServiceProvider.Object, _mockLogger.Object, _mockProviderRegistry.Object);

            // Act
            var storageService = factory.CreateStorageService(StorageProviderType.MinIO);

            // Assert
            Assert.NotNull(storageService);
            Assert.Equal(StorageProviderType.MinIO, storageService.ProviderType);
            Assert.Equal("MinIO", storageService.ProviderName);
        }

        [Fact]
        public void CreateStorageService_WithAwsS3Provider_ShouldReturnAwsS3Provider()
        {
            // Arrange
            var options = Options.Create(_configuration);
            var factory = new StorageServiceFactory(options, _mockServiceProvider.Object, _mockLogger.Object, _mockProviderRegistry.Object);

            // Act
            var storageService = factory.CreateStorageService(StorageProviderType.AwsS3);

            // Assert
            Assert.NotNull(storageService);
            Assert.Equal(StorageProviderType.AwsS3, storageService.ProviderType);
            Assert.Equal("AWS S3", storageService.ProviderName);
        }

        [Fact]
        public void CreateStorageService_WithFirebaseProvider_ShouldReturnFirebaseProvider()
        {
            // Arrange
            var options = Options.Create(_configuration);
            var factory = new StorageServiceFactory(options, _mockServiceProvider.Object, _mockLogger.Object, _mockProviderRegistry.Object);

            // Act
            var storageService = factory.CreateStorageService(StorageProviderType.Firebase);

            // Assert
            Assert.NotNull(storageService);
            Assert.Equal(StorageProviderType.Firebase, storageService.ProviderType);
            Assert.Equal("Firebase Storage", storageService.ProviderName);
        }

        [Fact]
        public void CreateStorageService_WithUnsupportedProvider_ShouldThrowStorageConfigurationException()
        {
            // Arrange
            var options = Options.Create(_configuration);
            var factory = new StorageServiceFactory(options, _mockServiceProvider.Object, _mockLogger.Object, _mockProviderRegistry.Object);

            // Act & Assert
            Assert.Throws<StorageConfigurationException>(() =>
                factory.CreateStorageService(StorageProviderType.AzureBlob));
        }

        [Fact]
        public void CreateStorageService_WithRegisteredProvider_ShouldReturnRegisteredProvider()
        {
            // Arrange
            var options = Options.Create(_configuration);
            var mockStorageService = new Mock<IStorageService>();
            mockStorageService.Setup(x => x.ProviderType).Returns(StorageProviderType.AzureBlob);

            _mockProviderRegistry.Setup(x => x.GetProviderFactory(StorageProviderType.AzureBlob))
                .Returns(() => mockStorageService.Object);

            var factory = new StorageServiceFactory(options, _mockServiceProvider.Object, _mockLogger.Object, _mockProviderRegistry.Object);

            // Act
            var storageService = factory.CreateStorageService(StorageProviderType.AzureBlob);

            // Assert
            Assert.NotNull(storageService);
            Assert.Equal(StorageProviderType.AzureBlob, storageService.ProviderType);
        }

        [Fact]
        public void GetAvailableProviders_ShouldReturnConfiguredProviders()
        {
            // Arrange
            var options = Options.Create(_configuration);
            var factory = new StorageServiceFactory(options, _mockServiceProvider.Object, _mockLogger.Object, _mockProviderRegistry.Object);

            // Act
            var availableProviders = factory.GetAvailableProviders().ToList();

            // Assert
            Assert.Contains(StorageProviderType.MinIO, availableProviders);
            Assert.Contains(StorageProviderType.AwsS3, availableProviders);
            Assert.Contains(StorageProviderType.Firebase, availableProviders);
        }

        [Fact]
        public void IsProviderAvailable_WithConfiguredProvider_ShouldReturnTrue()
        {
            // Arrange
            var options = Options.Create(_configuration);
            var factory = new StorageServiceFactory(options, _mockServiceProvider.Object, _mockLogger.Object, _mockProviderRegistry.Object);

            // Act & Assert
            Assert.True(factory.IsProviderAvailable(StorageProviderType.MinIO));
            Assert.True(factory.IsProviderAvailable(StorageProviderType.AwsS3));
            Assert.True(factory.IsProviderAvailable(StorageProviderType.Firebase));
        }

        [Fact]
        public void IsProviderAvailable_WithUnconfiguredProvider_ShouldReturnFalse()
        {
            // Arrange
            var options = Options.Create(_configuration);
            var factory = new StorageServiceFactory(options, _mockServiceProvider.Object, _mockLogger.Object, _mockProviderRegistry.Object);

            // Act & Assert
            Assert.False(factory.IsProviderAvailable(StorageProviderType.AzureBlob));
            Assert.False(factory.IsProviderAvailable(StorageProviderType.GoogleCloud));
        }

        [Fact]
        public void IsProviderAvailable_WithRegisteredProvider_ShouldReturnTrue()
        {
            // Arrange
            var options = Options.Create(_configuration);
            var mockStorageService = new Mock<IStorageService>();

            _mockProviderRegistry.Setup(x => x.GetProviderFactory(StorageProviderType.AzureBlob))
                .Returns(() => mockStorageService.Object);

            var factory = new StorageServiceFactory(options, _mockServiceProvider.Object, _mockLogger.Object, _mockProviderRegistry.Object);

            // Act & Assert
            Assert.True(factory.IsProviderAvailable(StorageProviderType.AzureBlob));
        }

        [Fact]
        public void CreateStorageService_WithInvalidMinIOConfiguration_ShouldThrowStorageConfigurationException()
        {
            // Arrange
            var invalidConfig = new ExternalStorageConfiguration
            {
                DefaultProvider = StorageProviderType.MinIO,
                MinIO = new MinIOStorageSettings
                {
                    Endpoint = "", // Invalid - empty endpoint
                    AccessKey = "test-access-key",
                    SecretKey = "test-secret-key"
                }
            };

            var options = Options.Create(invalidConfig);
            var factory = new StorageServiceFactory(options, _mockServiceProvider.Object, _mockLogger.Object, _mockProviderRegistry.Object);

            // Act & Assert
            Assert.Throws<StorageConfigurationException>(() =>
                factory.CreateStorageService(StorageProviderType.MinIO));
        }

        [Fact]
        public void CreateStorageService_WithMissingConfiguration_ShouldThrowStorageConfigurationException()
        {
            // Arrange
            var configWithoutMinIO = new ExternalStorageConfiguration
            {
                DefaultProvider = StorageProviderType.MinIO,
                MinIO = null // Missing configuration
            };

            var options = Options.Create(configWithoutMinIO);
            var factory = new StorageServiceFactory(options, _mockServiceProvider.Object, _mockLogger.Object, _mockProviderRegistry.Object);

            // Act & Assert
            Assert.Throws<StorageConfigurationException>(() =>
                factory.CreateStorageService(StorageProviderType.MinIO));
        }
    }
}
