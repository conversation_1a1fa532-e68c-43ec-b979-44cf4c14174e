using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace UNI.Utilities.MinIo.Extensions
{
    /// <summary>
    /// Extension methods for dependency injection
    /// </summary>
    public static class DependencyInjectionExtensions
    {
        /// <summary>
        /// Add MinIO service to the service collection
        /// </summary>
        /// <param name="services">Service collection</param>
        /// <param name="configurationSection">Configuration section containing MinIO settings</param>
        /// <returns>Service collection for chaining</returns>
        public static IServiceCollection AddMinIoService(
            this IServiceCollection services,
            IConfigurationSection configurationSection)
        {
            services.Configure<MinIoSettings>(configurationSection);
            services.AddSingleton<IMinIoService, MinIoService>();
            return services;
        }

        /// <summary>
        /// Add MinIO service to the service collection with settings
        /// </summary>
        /// <param name="services">Service collection</param>
        /// <param name="settings">MinIO settings</param>
        /// <returns>Service collection for chaining</returns>
        public static IServiceCollection AddMinIoService(
            this IServiceCollection services,
            MinIoSettings settings)
        {
            services.AddSingleton(settings);
            services.AddSingleton<IMinIoService>(provider =>
            {
                var logger = provider.GetRequiredService<ILogger<MinIoService>>();
                return new MinIoService(settings, logger);
            });
            return services;
        }

        /// <summary>
        /// Add MinIO service to the service collection with configuration action
        /// </summary>
        /// <param name="services">Service collection</param>
        /// <param name="configureSettings">Action to configure MinIO settings</param>
        /// <returns>Service collection for chaining</returns>
        public static IServiceCollection AddMinIoService(
            this IServiceCollection services,
            Action<MinIoSettings> configureSettings)
        {
            var settings = new MinIoSettings();
            configureSettings(settings);
            
            return services.AddMinIoService(settings);
        }
    }
}
