﻿
using System;
using System.Threading.Tasks;
using UNI.Common.CommonBase;
using UNI.Model;
using Uni.Personal.Model;

namespace Uni.Personal.DAL.Interfaces
{
    /// <summary>
    /// Order repository interface
    /// </summary>
    public interface ICustomerRepository
    {
        // Inherits all methods from IBaseRepository:
        Task<CommonListPage> GetPageAsync(CustomerFilterInput? filter);
        Task<CommonViewOidInfo> GetInfoAsync(Guid? oid);
        Task<BaseValidate<Guid?>> SetInfoAsync(CommonViewOidInfo info);
        Task<BaseValidate> DeleteAsync(Guid? oid);

    }
}
