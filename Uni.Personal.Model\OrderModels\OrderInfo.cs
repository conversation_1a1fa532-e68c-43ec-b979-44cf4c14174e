using UNI.Model;

namespace Uni.Personal.Model
{
    /// <summary>
    /// Order information model for create/update operations
    /// </summary>
    public class OrderInfo : CommonViewOidInfo
    {
        public string? OrderCode { get; set; }
        public string? CustomerName { get; set; }
        public string? CustomerEmail { get; set; }
        public string? CustomerPhone { get; set; }
        public string? ProductType { get; set; }
        public decimal? Amount { get; set; }
        public int? Status { get; set; }
        public DateTime? OrderDate { get; set; }
        public string? Description { get; set; }
        public string? Notes { get; set; }
    }
}
