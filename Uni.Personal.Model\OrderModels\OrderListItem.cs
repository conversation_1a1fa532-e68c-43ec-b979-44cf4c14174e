using System;

namespace Uni.Personal.Model
{

    /// <summary>
    /// Order list item for display in grid
    /// </summary>
    public class OrderListItem
    {
        public Guid Oid { get; set; }
        public string? OrderCode { get; set; }
        public string? CustomerName { get; set; }
        public string? CustomerEmail { get; set; }
        public string? ProductType { get; set; }
        public decimal? Amount { get; set; }
        public int? Status { get; set; }
        public string? StatusText { get; set; }
        public DateTime? OrderDate { get; set; }
        public DateTime? CreatedDate { get; set; }
        public string? CreatedBy { get; set; }
    }
}
