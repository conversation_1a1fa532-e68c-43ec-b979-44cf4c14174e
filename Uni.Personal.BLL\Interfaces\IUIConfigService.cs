using UNI.Model;

namespace Uni.Personal.BLL.Interfaces
{
    /// <summary>
    /// UIConfig service interface
    /// </summary>
    public interface IUIConfigService
    {
        /// <summary>
        /// Get configuration language filter
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="acceptLanguage">Accept language</param>
        /// <returns>Configuration language filter</returns>
        Task<CommonViewInfo> GetConfigLanguageFilter(string userId, string acceptLanguage);

        /// <summary>
        /// Get form view page
        /// </summary>
        /// <param name="filter">Table key filter</param>
        /// <returns>Form view page</returns>
        Task<CommonListPage> GetFormViewPage(FilterInpTableKey filter);

        /// <summary>
        /// Set form view information
        /// </summary>
        /// <param name="para">Configuration field</param>
        /// <returns>Validation result</returns>
        Task<BaseValidate> SetFormViewInfo(ConfigField para);

        /// <summary>
        /// Delete form view information
        /// </summary>
        /// <param name="id">Form view ID</param>
        /// <returns>Validation result</returns>
        Task<BaseValidate> DelFormViewInfo(long id);

        /// <summary>
        /// Get grid view filter
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="acceptLanguage">Accept language</param>
        /// <returns>Grid view filter</returns>
        Task<CommonViewInfo> GetGridViewFilter(string userId, string acceptLanguage);

        /// <summary>
        /// Get grid view page
        /// </summary>
        /// <param name="filter">Grid key filter</param>
        /// <returns>Grid view page</returns>
        Task<CommonListPage> GetGridViewPage(FilterInpGridKey filter);

        /// <summary>
        /// Set grid view information
        /// </summary>
        /// <param name="para">Configuration column</param>
        /// <returns>Validation result</returns>
        Task<BaseValidate> SetGridViewInfo(ConfigColumn para);

        /// <summary>
        /// Delete grid view information
        /// </summary>
        /// <param name="gridId">Grid ID</param>
        /// <returns>Validation result</returns>
        Task<BaseValidate> DelGridViewInfo(long gridId);

        /// <summary>
        /// Get group information
        /// </summary>
        /// <param name="key_1">Key 1</param>
        /// <param name="key_2">Key 2</param>
        /// <returns>Group information</returns>
        Task<CommonViewInfo> GetGroupInfo(string key_1, string key_2);

        /// <summary>
        /// Set group information
        /// </summary>
        /// <param name="para">Common view information</param>
        /// <returns>Validation result</returns>
        Task<BaseValidate> SetGroupInfo(CommonViewInfo para);

        /// <summary>
        /// Set filter information
        /// </summary>
        /// <param name="gridKey">Grid key</param>
        /// <param name="para">Filter parameters</param>
        /// <returns>Task</returns>
        Task SetFilterInfo(string gridKey, Dictionary<string, string> para);
    }
}
