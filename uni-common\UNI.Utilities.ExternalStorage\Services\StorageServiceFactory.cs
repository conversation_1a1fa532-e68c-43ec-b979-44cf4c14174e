using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using UNI.Utilities.ExternalStorage.Abstractions;
using UNI.Utilities.ExternalStorage.Exceptions;
using UNI.Utilities.ExternalStorage.Models;
using UNI.Utilities.ExternalStorage.Providers;

namespace UNI.Utilities.ExternalStorage.Services
{
    /// <summary>
    /// Factory for creating storage service instances
    /// </summary>
    public class StorageServiceFactory : IStorageServiceFactory
    {
        private readonly ExternalStorageConfiguration _configuration;
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<StorageServiceFactory> _logger;
        private readonly IStorageProviderRegistry _providerRegistry;

        /// <summary>
        /// Constructor for StorageServiceFactory
        /// </summary>
        /// <param name="configuration">External storage configuration</param>
        /// <param name="serviceProvider">Service provider for dependency injection</param>
        /// <param name="logger">Logger instance</param>
        /// <param name="providerRegistry">Provider registry</param>
        public StorageServiceFactory(
            IOptions<ExternalStorageConfiguration> configuration,
            IServiceProvider serviceProvider,
            ILogger<StorageServiceFactory> logger,
            IStorageProviderRegistry providerRegistry)
        {
            _configuration = configuration.Value ?? throw new ArgumentNullException(nameof(configuration));
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _providerRegistry = providerRegistry ?? throw new ArgumentNullException(nameof(providerRegistry));
        }

        /// <summary>
        /// Create a storage service instance for the specified provider type
        /// </summary>
        /// <param name="providerType">Storage provider type</param>
        /// <returns>Storage service instance</returns>
        public IStorageService CreateStorageService(StorageProviderType providerType)
        {
            try
            {
                _logger.LogDebug("Creating storage service for provider type: {ProviderType}", providerType);

                // First try to get from registry
                var factory = _providerRegistry.GetProviderFactory(providerType);
                if (factory != null)
                {
                    return factory();
                }

                // Fall back to built-in providers
                return providerType switch
                {
                    StorageProviderType.MinIO => CreateMinIOProvider(),
                    StorageProviderType.AwsS3 => CreateAwsS3Provider(),
                    StorageProviderType.Firebase => CreateFirebaseProvider(),
                    _ => throw new StorageConfigurationException($"Unsupported storage provider type: {providerType}")
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to create storage service for provider type: {ProviderType}", providerType);
                throw;
            }
        }

        /// <summary>
        /// Create the default storage service instance
        /// </summary>
        /// <returns>Default storage service instance</returns>
        public IStorageService CreateDefaultStorageService()
        {
            return CreateStorageService(_configuration.DefaultProvider);
        }

        /// <summary>
        /// Get all available storage provider types
        /// </summary>
        /// <returns>Available provider types</returns>
        public IEnumerable<StorageProviderType> GetAvailableProviders()
        {
            var availableProviders = new List<StorageProviderType>();

            // Check built-in providers
            if (IsProviderConfigured(StorageProviderType.MinIO))
                availableProviders.Add(StorageProviderType.MinIO);

            if (IsProviderConfigured(StorageProviderType.AwsS3))
                availableProviders.Add(StorageProviderType.AwsS3);

            // if (IsProviderConfigured(StorageProviderType.Firebase))
            //     availableProviders.Add(StorageProviderType.Firebase); // Temporarily disabled

            // Add registered providers
            availableProviders.AddRange(_providerRegistry.GetRegisteredProviders());

            return availableProviders.Distinct();
        }

        /// <summary>
        /// Check if a storage provider is available/configured
        /// </summary>
        /// <param name="providerType">Storage provider type</param>
        /// <returns>True if provider is available, false otherwise</returns>
        public bool IsProviderAvailable(StorageProviderType providerType)
        {
            // Check if registered
            if (_providerRegistry.GetProviderFactory(providerType) != null)
                return true;

            // Check built-in providers
            return IsProviderConfigured(providerType);
        }

        #region Private Methods

        private bool IsProviderConfigured(StorageProviderType providerType)
        {
            return providerType switch
            {
                StorageProviderType.MinIO => _configuration.MinIO != null && IsMinIOConfigurationValid(_configuration.MinIO),
                StorageProviderType.AwsS3 => _configuration.AwsS3 != null && IsAwsS3ConfigurationValid(_configuration.AwsS3),
                // StorageProviderType.Firebase => _configuration.Firebase != null && IsFirebaseConfigurationValid(_configuration.Firebase), // Temporarily disabled
                _ => false
            };
        }

        private bool IsMinIOConfigurationValid(MinIOStorageSettings settings)
        {
            return !string.IsNullOrWhiteSpace(settings.Endpoint) &&
                   !string.IsNullOrWhiteSpace(settings.AccessKey) &&
                   !string.IsNullOrWhiteSpace(settings.SecretKey);
        }

        private bool IsAwsS3ConfigurationValid(AwsS3StorageSettings settings)
        {
            return !string.IsNullOrWhiteSpace(settings.AccessKeyId) &&
                   !string.IsNullOrWhiteSpace(settings.SecretAccessKey) &&
                   !string.IsNullOrWhiteSpace(settings.Region);
        }

        private bool IsFirebaseConfigurationValid(FirebaseStorageSettings settings)
        {
            return !string.IsNullOrWhiteSpace(settings.ProjectId) &&
                   (!string.IsNullOrWhiteSpace(settings.ServiceAccountKeyPath) ||
                    !string.IsNullOrWhiteSpace(settings.ServiceAccountKeyJson));
        }

        private IStorageService CreateMinIOProvider()
        {
            if (_configuration.MinIO == null)
                throw new StorageConfigurationException("MinIO configuration is not provided", StorageProviderType.MinIO);

            var logger = _serviceProvider.GetService(typeof(ILogger<MinIOStorageProvider>)) as ILogger<MinIOStorageProvider>
                ?? throw new InvalidOperationException("Logger for MinIOStorageProvider not found");

            return new MinIOStorageProvider(_configuration.MinIO, logger);
        }

        private IStorageService CreateAwsS3Provider()
        {
            if (_configuration.AwsS3 == null)
                throw new StorageConfigurationException("AWS S3 configuration is not provided", StorageProviderType.AwsS3);

            var logger = _serviceProvider.GetService(typeof(ILogger<AwsS3StorageProvider>)) as ILogger<AwsS3StorageProvider>
                ?? throw new InvalidOperationException("Logger for AwsS3StorageProvider not found");

            return new AwsS3StorageProvider(_configuration.AwsS3, logger);
        }

        private IStorageService CreateFirebaseProvider()
        {
            if (_configuration.Firebase == null)
                throw new StorageConfigurationException("Firebase configuration is not provided", StorageProviderType.Firebase);

            var logger = _serviceProvider.GetService(typeof(ILogger<FirebaseStorageProvider>)) as ILogger<FirebaseStorageProvider>
                ?? throw new InvalidOperationException("Logger for FirebaseStorageProvider not found");

            return new FirebaseStorageProvider(_configuration.Firebase, logger);
        }

        #endregion
    }

    /// <summary>
    /// Registry for storage providers
    /// </summary>
    public class StorageProviderRegistry : IStorageProviderRegistry
    {
        private readonly Dictionary<StorageProviderType, Func<IStorageService>> _providers = new();
        private readonly object _lock = new();

        /// <summary>
        /// Register a storage provider
        /// </summary>
        /// <param name="providerType">Provider type</param>
        /// <param name="factory">Factory function to create the provider</param>
        public void RegisterProvider(StorageProviderType providerType, Func<IStorageService> factory)
        {
            if (factory == null)
                throw new ArgumentNullException(nameof(factory));

            lock (_lock)
            {
                _providers[providerType] = factory;
            }
        }

        /// <summary>
        /// Unregister a storage provider
        /// </summary>
        /// <param name="providerType">Provider type</param>
        public void UnregisterProvider(StorageProviderType providerType)
        {
            lock (_lock)
            {
                _providers.Remove(providerType);
            }
        }

        /// <summary>
        /// Get a storage provider factory
        /// </summary>
        /// <param name="providerType">Provider type</param>
        /// <returns>Factory function or null if not registered</returns>
        public Func<IStorageService>? GetProviderFactory(StorageProviderType providerType)
        {
            lock (_lock)
            {
                return _providers.TryGetValue(providerType, out var factory) ? factory : null;
            }
        }

        /// <summary>
        /// Get all registered provider types
        /// </summary>
        /// <returns>Registered provider types</returns>
        public IEnumerable<StorageProviderType> GetRegisteredProviders()
        {
            lock (_lock)
            {
                return _providers.Keys.ToList();
            }
        }
    }
}
