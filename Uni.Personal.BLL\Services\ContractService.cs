using UNI.Model;
using Uni.Personal.BLL.Interfaces;
using Uni.Personal.DAL.Interfaces;
using Uni.Personal.Model;

namespace Uni.Personal.BLL.Services
{
    /// <summary>
    /// Contract service implementation
    /// </summary>
    public class ContractService : IContractService
    {
        private readonly IContractRepository _contractRepository;

        public ContractService(IContractRepository contractRepository)
        {
            _contractRepository = contractRepository;
        }

        /// <summary>
        /// Get paginated list of contracts
        /// </summary>
        /// <param name="query">Filter input for contracts</param>
        /// <returns>Paginated list of contracts</returns>
        public async Task<CommonListPage> GetPageAsync(ContractFilterInput? query)
        {
            return await _contractRepository.GetPageAsync(query);
        }
    }
}
