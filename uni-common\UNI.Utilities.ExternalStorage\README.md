# UNI.Utilities.ExternalStorage

A comprehensive .NET library for external storage operations with support for multiple cloud storage providers including MinIO, AWS S3, Firebase Storage, and more. This library provides a unified interface for storage operations while allowing easy switching between different storage providers through configuration.

## Features

- **Multi-Provider Support**: MinIO, AWS S3, Firebase Storage, Azure Blob Storage, Google Cloud Storage
- **Unified Interface**: Single `IStorageService` interface for all storage operations
- **Provider Factory**: Easy switching between storage providers via configuration
- **Dependency Injection**: Full support for .NET DI container with easy service registration
- **Async/Await**: Complete async support with cancellation tokens
- **Comprehensive Operations**: Bucket/container management, object upload/download, presigned URLs
- **Progress Tracking**: Upload progress callbacks for large file operations
- **Error Handling**: Custom exceptions with detailed error information
- **Extensible**: Plugin architecture for custom storage providers
- **Fallback Support**: Automatic fallback to secondary providers when primary fails
- **Configuration Management**: Flexible configuration via appsettings.json or environment variables

## Installation

Add the project reference to your application:

```xml
<ItemGroup>
  <ProjectReference Include="..\UNI.Utilities.ExternalStorage\UNI.Utilities.ExternalStorage.csproj" />
</ItemGroup>
```

## Quick Start

### 1. Configuration

Add storage configuration to your `appsettings.json`:

```json
{
  "ExternalStorage": {
    "DefaultProvider": "MinIO",
    "EnableFallback": false,
    "FallbackOrder": [ "AwsS3", "Firebase" ],
    "MinIO": {
      "Endpoint": "localhost:9000",
      "ProxyEndpoint": "https://your-proxy-domain.com",
      "AccessKey": "your-access-key",
      "SecretKey": "your-secret-key",
      "UseSSL": false,
      "Region": "us-east-1",
      "DefaultBucket": "default-bucket",
      "TimeoutSeconds": 30,
      "CreateBucketIfNotExists": true
    },
    "AwsS3": {
      "AccessKeyId": "your-aws-access-key-id",
      "SecretAccessKey": "your-aws-secret-access-key",
      "Region": "us-east-1",
      "DefaultBucket": "your-s3-bucket",
      "TimeoutSeconds": 30,
      "CreateBucketIfNotExists": true
    },
    "Firebase": {
      "ProjectId": "your-firebase-project-id",
      "ServiceAccountKeyPath": "/path/to/service-account-key.json",
      "StorageBucket": "your-project-id.appspot.com",
      "DefaultBucket": "your-firebase-bucket",
      "TimeoutSeconds": 30,
      "CreateBucketIfNotExists": true
    }
  }
}
```

### 2. Service Registration

Register the storage services in your `Startup.cs` or `Program.cs`:

```csharp
public class Startup
{
    public void ConfigureServices(IServiceCollection services)
    {
        // Register external storage services
        services.AddExternalStorageServices(Configuration.GetSection("ExternalStorage"));
        
        // Or configure programmatically
        services.AddExternalStorageServices(config =>
        {
            config.DefaultProvider = StorageProviderType.MinIO;
            config.MinIO = new MinIOStorageSettings
            {
                Endpoint = "localhost:9000",
                AccessKey = "your-access-key",
                SecretKey = "your-secret-key",
                UseSSL = false
            };
        });
    }
}
```

### 3. Basic Usage

Inject and use the storage service:

```csharp
public class FileService
{
    private readonly IStorageService _storageService;

    public FileService(IStorageService storageService)
    {
        _storageService = storageService;
    }

    // Upload a file
    public async Task<StorageUploadResult> UploadFileAsync(string filePath, string bucketName, string objectName)
    {
        return await _storageService.UploadFileAsync(bucketName, objectName, filePath);
    }

    // Upload from stream
    public async Task<StorageUploadResult> UploadStreamAsync(Stream stream, string bucketName, string objectName)
    {
        return await _storageService.UploadObjectAsync(bucketName, objectName, stream);
    }

    // Download to file
    public async Task DownloadFileAsync(string bucketName, string objectName, string filePath)
    {
        await _storageService.DownloadFileAsync(bucketName, objectName, filePath);
    }

    // Get presigned download URL
    public async Task<string> GetDownloadUrlAsync(string bucketName, string objectName)
    {
        return await _storageService.GetPresignedDownloadUrlAsync(bucketName, objectName, TimeSpan.FromHours(1));
    }

    // List objects
    public async Task<IEnumerable<StorageObjectInfo>> ListFilesAsync(string bucketName, string prefix = null)
    {
        return await _storageService.ListObjectsAsync(bucketName, prefix);
    }
}
```

## Supported Storage Providers

### MinIO
- Self-hosted S3-compatible object storage
- Supports all S3 operations
- Proxy endpoint support for public access

### AWS S3
- Amazon Simple Storage Service
- Full S3 API support
- Server-side encryption
- Storage classes
- Multipart uploads

### Firebase Storage
- Google Firebase Cloud Storage
- Service account authentication
- Firebase emulator support
- Custom download domains

### Azure Blob Storage (Coming Soon)
- Microsoft Azure Blob Storage
- Hot, Cool, and Archive tiers
- SAS token authentication

### Google Cloud Storage (Coming Soon)
- Google Cloud Platform Storage
- Service account authentication
- Multiple storage classes

## Advanced Configuration

### Provider-Specific Registration

Register individual providers:

```csharp
// Register MinIO provider
services.AddMinIOStorageProvider(Configuration.GetSection("MinIO"));

// Register AWS S3 provider
services.AddAwsS3StorageProvider(new AwsS3StorageSettings
{
    AccessKeyId = "your-access-key-id",
    SecretAccessKey = "your-secret-access-key",
    Region = "us-east-1"
});

// Register Firebase provider
services.AddFirebaseStorageProvider(Configuration.GetSection("Firebase"));
```

### Fallback Configuration

Enable automatic fallback to secondary providers:

```csharp
services.AddStorageServiceWithFallback(
    StorageProviderType.MinIO,           // Primary provider
    StorageProviderType.AwsS3,          // First fallback
    StorageProviderType.Firebase        // Second fallback
);
```

### Custom Provider Registration

Register custom storage providers:

```csharp
services.RegisterStorageProvider(StorageProviderType.AzureBlob, provider =>
{
    var settings = provider.GetRequiredService<AzureBlobStorageSettings>();
    var logger = provider.GetRequiredService<ILogger<AzureBlobStorageProvider>>();
    return new AzureBlobStorageProvider(settings, logger);
});
```

### Factory Pattern Usage

Use the factory to create specific providers:

```csharp
public class MultiProviderService
{
    private readonly IStorageServiceFactory _factory;

    public MultiProviderService(IStorageServiceFactory factory)
    {
        _factory = factory;
    }

    public async Task MigrateData()
    {
        var sourceStorage = _factory.CreateStorageService(StorageProviderType.MinIO);
        var targetStorage = _factory.CreateStorageService(StorageProviderType.AwsS3);

        // Migrate data between providers
        var objects = await sourceStorage.ListObjectsAsync("source-bucket");
        foreach (var obj in objects)
        {
            var data = await sourceStorage.GetObjectBytesAsync("source-bucket", obj.ObjectName);
            await targetStorage.UploadObjectAsync("target-bucket", obj.ObjectName, new MemoryStream(data));
        }
    }
}
```

## API Reference

### IStorageService Interface

The main interface provides the following method categories:

#### Bucket Operations
- `BucketExistsAsync`: Check if a bucket exists
- `CreateBucketAsync`: Create a new bucket
- `DeleteBucketAsync`: Delete a bucket
- `ListBucketsAsync`: List all buckets

#### Object Operations
- `UploadObjectAsync`: Upload from stream
- `UploadFileAsync`: Upload from file
- `UploadLargeFileAsync`: Upload large files with progress tracking
- `DownloadObjectAsync`: Download to stream
- `DownloadFileAsync`: Download to file
- `GetObjectBytesAsync`: Get object as byte array
- `ObjectExistsAsync`: Check if object exists
- `DeleteObjectAsync`: Delete single object
- `DeleteObjectsAsync`: Delete multiple objects
- `GetObjectInfoAsync`: Get object metadata
- `ListObjectsAsync`: List objects in bucket

#### URL Operations
- `GetPresignedDownloadUrlAsync`: Generate download URL
- `GetPresignedUploadUrlAsync`: Generate upload URL
- `GetPreviewUrlAsync`: Get public preview URL

### Configuration Classes

#### ExternalStorageConfiguration
Main configuration class containing provider settings and fallback configuration.

#### Provider-Specific Settings
- `MinIOStorageSettings`: MinIO configuration
- `AwsS3StorageSettings`: AWS S3 configuration
- `FirebaseStorageSettings`: Firebase configuration

### Models

#### StorageUploadResult
Contains information about uploaded objects including size, ETag, and provider-specific data.

#### StorageObjectInfo
Metadata about storage objects including size, last modified date, and content type.

#### StorageUploadProgress
Progress information for large file uploads including bytes transferred and estimated time remaining.

## Error Handling

The library provides comprehensive error handling with custom exceptions:

```csharp
try
{
    await storageService.UploadFileAsync("bucket", "object", "file.txt");
}
catch (BucketNotFoundException ex)
{
    // Handle bucket not found
}
catch (ObjectNotFoundException ex)
{
    // Handle object not found
}
catch (StorageAccessDeniedException ex)
{
    // Handle access denied
}
catch (StorageException ex)
{
    // Handle general storage errors
}
```

## Testing

The library includes comprehensive unit and integration tests:

```bash
# Run unit tests
dotnet test UNI.Utilities.ExternalStorage.Tests --filter Category=Unit

# Run integration tests (requires storage provider configuration)
dotnet test UNI.Utilities.ExternalStorage.Tests --filter Category=Integration
```

## Environment Variables

You can configure storage providers using environment variables:

```bash
# MinIO
EXTERNALSTORAGE__MINIO__ENDPOINT=localhost:9000
EXTERNALSTORAGE__MINIO__ACCESSKEY=your-access-key
EXTERNALSTORAGE__MINIO__SECRETKEY=your-secret-key

# AWS S3
EXTERNALSTORAGE__AWSS3__ACCESSKEYID=your-access-key-id
EXTERNALSTORAGE__AWSS3__SECRETACCESSKEY=your-secret-access-key
EXTERNALSTORAGE__AWSS3__REGION=us-east-1

# Firebase
EXTERNALSTORAGE__FIREBASE__PROJECTID=your-project-id
EXTERNALSTORAGE__FIREBASE__SERVICEACCOUNTKEYJSON={"type":"service_account",...}
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## Examples

See the `Examples` folder for complete working examples:

- **BasicUsage**: Simple file upload/download operations
- **MultiProvider**: Using multiple storage providers
- **ProgressTracking**: Large file uploads with progress callbacks
- **Configuration**: Different configuration approaches
- **CustomProvider**: Implementing custom storage providers

## Performance Considerations

- Use `UploadLargeFileAsync` for files larger than 64MB
- Enable multipart uploads for better performance on large files
- Consider using presigned URLs for direct client uploads
- Implement proper retry logic for production applications
- Use connection pooling for high-throughput scenarios

## Security Best Practices

- Store credentials securely (Azure Key Vault, AWS Secrets Manager, etc.)
- Use IAM roles and service accounts instead of access keys when possible
- Implement proper access controls on storage buckets
- Use HTTPS/SSL for all storage operations
- Regularly rotate access keys and credentials
- Validate file types and sizes before upload
- Implement virus scanning for uploaded files

## Troubleshooting

### Common Issues

1. **Authentication Errors**: Verify credentials and permissions
2. **Network Timeouts**: Increase timeout settings or check network connectivity
3. **Bucket Not Found**: Ensure bucket exists or enable auto-creation
4. **Large File Uploads**: Use `UploadLargeFileAsync` for files > 64MB
5. **Presigned URL Expiry**: Check URL expiration times

### Logging

Enable detailed logging to troubleshoot issues:

```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "UNI.Utilities.ExternalStorage": "Debug"
    }
  }
}
```

## License

This project is licensed under the MIT License.
