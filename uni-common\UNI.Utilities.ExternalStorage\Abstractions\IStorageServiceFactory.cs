using UNI.Utilities.ExternalStorage.Models;

namespace UNI.Utilities.ExternalStorage.Abstractions
{
    /// <summary>
    /// Factory interface for creating storage service instances
    /// </summary>
    public interface IStorageServiceFactory
    {
        /// <summary>
        /// Create a storage service instance for the specified provider type
        /// </summary>
        /// <param name="providerType">Storage provider type</param>
        /// <returns>Storage service instance</returns>
        IStorageService CreateStorageService(StorageProviderType providerType);

        /// <summary>
        /// Create the default storage service instance
        /// </summary>
        /// <returns>Default storage service instance</returns>
        IStorageService CreateDefaultStorageService();

        /// <summary>
        /// Get all available storage provider types
        /// </summary>
        /// <returns>Available provider types</returns>
        IEnumerable<StorageProviderType> GetAvailableProviders();

        /// <summary>
        /// Check if a storage provider is available/configured
        /// </summary>
        /// <param name="providerType">Storage provider type</param>
        /// <returns>True if provider is available, false otherwise</returns>
        bool IsProviderAvailable(StorageProviderType providerType);
    }

    /// <summary>
    /// Interface for storage provider registration
    /// </summary>
    public interface IStorageProviderRegistry
    {
        /// <summary>
        /// Register a storage provider
        /// </summary>
        /// <param name="providerType">Provider type</param>
        /// <param name="factory">Factory function to create the provider</param>
        void RegisterProvider(StorageProviderType providerType, Func<IStorageService> factory);

        /// <summary>
        /// Unregister a storage provider
        /// </summary>
        /// <param name="providerType">Provider type</param>
        void UnregisterProvider(StorageProviderType providerType);

        /// <summary>
        /// Get a storage provider factory
        /// </summary>
        /// <param name="providerType">Provider type</param>
        /// <returns>Factory function or null if not registered</returns>
        Func<IStorageService>? GetProviderFactory(StorageProviderType providerType);

        /// <summary>
        /// Get all registered provider types
        /// </summary>
        /// <returns>Registered provider types</returns>
        IEnumerable<StorageProviderType> GetRegisteredProviders();
    }
}
