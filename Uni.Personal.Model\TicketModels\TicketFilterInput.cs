using UNI.Model;

namespace Uni.Personal.Model
{
    /// <summary>
    /// Filter input for Order pagination
    /// </summary>
    public class TicketFilterInput : FilterInput
    {
        public string? ticket_no { get; set; }
        public int? request_type_id { get; set; }
        public string? customer_id { get; set; }
        public string? subject { get; set; }
        public string? content { get; set; }
        public int? status { get; set; }
    }
}
