using System;
using System.Collections.Generic;

namespace Uni.Personal.Model
{
    /// <summary>
    /// Order detail view model for displaying detailed order information including items
    /// </summary>
    public class OrderDetailView
    {
        public Guid Oid { get; set; }
        public string? OrderCode { get; set; }
        public string? ContractCode { get; set; }
        public string? CustomerName { get; set; }
        public string? CustomerEmail { get; set; }
        public string? CustomerPhone { get; set; }

        //Tổng trước VAT
        public decimal? SubTotal { get; set; }
        public decimal? VatRate { get; set; }
        public decimal? VatAmount { get; set; }
        ///Thành tiền (sau VAT)
        public decimal? TotalAmount { get; set; }

        public int? Status { get; set; }
        public string? StatusName { get; set; }
        public string? StatusLabel { get; set; }
        public DateTime? OrderDate { get; set; }
        public string? Description { get; set; }
        public string? Notes { get; set; }
        public List<OrderItemView> OrderItems { get; set; } = new List<OrderItemView>();
    }
}
