using System.Threading.Tasks;
using UNI.Common.CommonBase;
using UNI.Model;
using Uni.Personal.DAL.Interfaces;
using Uni.Personal.Model;

namespace Uni.Personal.DAL.Repositories
{
    /// <summary>
    /// Contract repository implementation
    /// </summary>
    public class ContractRepository : UniBaseRepository, IContractRepository
    {
        public ContractRepository(IUniCommonBaseRepository commonBase) : base(commonBase)
        {
        }

        /// <summary>
        /// Get paginated list of contracts
        /// </summary>
        /// <param name="filter">Filter criteria</param>
        /// <returns>Paginated list of contracts</returns>
        public async Task<CommonListPage> GetPageAsync(ContractFilterInput filter)
        {
            // Call stored procedure to get paginated contracts
            return await GetPageAsync("sp_personal_contract_page", filter, param =>
            {
                // Add custom parameters if needed
                param.Add("@ContractNo", filter.ContractNo);
                param.Add("@OrderCode", filter.OrderCode);
                param.Add("@CustomerName", filter.CustomerName);
                param.Add("@FromDate", filter.FromDate);
                param.Add("@ToDate", filter.ToDate);
                param.Add("@Status", filter.Status);
                return param;
            });
        }
    }
}
