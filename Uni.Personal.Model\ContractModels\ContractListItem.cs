using System;

namespace Uni.Personal.Model
{
    /// <summary>
    /// Contract list item for display in grid
    /// </summary>
    public class ContractListItem
    {
        public Guid Oid { get; set; }
        public Guid? CustomerId { get; set; }
        public Guid? OrderId { get; set; }
        public Guid? OrderDetailsId { get; set; }
        public string? ContractNo { get; set; }
        public string? ContractDate { get; set; }
        public string? StatusText { get; set; }
        public string? OrderStatusText { get; set; }
        public string? OrderPaymentStatusText { get; set; }
        public string? OrderName { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public int? OrderStatus { get; set; }
        public int? OrderPaymentStatus { get; set; }
    }
}
