<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.16" />
    <PackageReference Include="OpenTelemetry.Exporter.Console" Version="1.9.0" />
    <PackageReference Include="OpenTelemetry.Exporter.OpenTelemetryProtocol" Version="1.9.0" />
    <PackageReference Include="OpenTelemetry.Extensions.Hosting" Version="1.9.0" />
    <PackageReference Include="OpenTelemetry.Instrumentation.AspNetCore" Version="1.9.0" />
    <PackageReference Include="OpenTelemetry.Instrumentation.Http" Version="1.9.0" />
    <PackageReference Include="OpenTelemetry.Instrumentation.Process" Version="0.5.0-beta.6" />
    <PackageReference Include="OpenTelemetry.Instrumentation.Runtime" Version="1.9.0" />
    <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
    <PackageReference Include="Serilog.Sinks.Async" Version="2.1.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="8.1.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\uni-common\UNI.Model\UNI.Model.csproj" />
    <ProjectReference Include="..\Uni.Personal.BLL\Uni.Personal.BLL.csproj" />
    <ProjectReference Include="..\Uni.Personal.Model\Uni.Personal.Model.csproj" />
    <ProjectReference Include="..\uni-common\UNI.Utilities.ExternalStorage\UNI.Utilities.ExternalStorage.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="logs\" />
  </ItemGroup>

  <ItemGroup>
    <None Include="Properties\PublishProfiles\webdebloy_dev.pubxml.user" />
  </ItemGroup>

</Project>
