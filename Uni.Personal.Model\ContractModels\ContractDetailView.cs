using System;

namespace Uni.Personal.Model
{
    /// <summary>
    /// Contract detail view model for displaying detailed contract information
    /// </summary>
    public class ContractDetailView
    {
        public Guid Oid { get; set; }
        public Guid? OrderId { get; set; }
        public Guid? OrderDetailsId { get; set; }
        public string? ContractNo { get; set; }
        public DateTime? ContractDate { get; set; }
        public int? Status { get; set; }
        public string? StatusText { get; set; }
        public string? ContractLink { get; set; }
        
        // Order information
        public string? OrderCode { get; set; }
        public string? OrderName { get; set; }
        public string? CustomerName { get; set; }
        public string? CustomerEmail { get; set; }
        public string? CustomerPhone { get; set; }
        
        // Order status information
        public int? OrderStatus { get; set; }
        public string? OrderStatusText { get; set; }
        public int? OrderPaymentStatus { get; set; }
        public string? OrderPaymentStatusText { get; set; }
        
        // Dates
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public string? CreatedBy { get; set; }
        public string? UpdatedBy { get; set; }
    }
}
