﻿using System;
using System.Threading.Tasks;
using UNI.Common.CommonBase;
using UNI.Model;
using Uni.Personal.BLL.Interfaces;
using Uni.Personal.DAL.Interfaces;
using Uni.Personal.Model;
using Uni.Personal.DAL.Repositories;

namespace Uni.Personal.BLL.Services
{
    /// <summary>
    /// Order service implementation
    /// </summary>
    public class CustomerService : ICustomerService
    {
        private readonly ICustomerRepository _customerRepository;

        public CustomerService(ICustomerRepository customerRepository)
        {
            _customerRepository = customerRepository;
        }

        /// <summary>
        /// Get paginated list of orders
        /// </summary>
        /// <param name="query">Filter input for orders</param>
        /// <returns>Paginated list of orders</returns>
        public async Task<CommonListPage> GetPageAsync(CustomerFilterInput? query)
        {
            return await _customerRepository.GetPageAsync(query);
        }

        /// <summary>
        /// Get order information by ID
        /// </summary>
        /// <param name="oid">Order ID</param>
        /// <returns>Order information</returns>
        public async Task<CommonViewOidInfo> GetInfoAsync(Guid? oid)
        {
            return await _customerRepository.GetInfoAsync(oid);
        }

        /// <summary>
        /// Create or update order
        /// </summary>
        /// <param name="info">Order information</param>
        /// <returns>Validation result with order ID</returns>
        public async Task<BaseValidate<Guid?>> SetInfoAsync(CommonViewOidInfo info)
        {
            return await _customerRepository.SetInfoAsync(info);
        }

        /// <summary>
        /// Delete order
        /// </summary>
        /// <param name="oid">Order ID</param>
        /// <returns>Validation result</returns>
        public async Task<BaseValidate> DeleteAsync(Guid? oid)
        {
            // Add business logic validation here if needed
            // For example: check if order can be deleted, validate permissions, etc.

            return await _customerRepository.DeleteAsync(oid);
        }

    }
}
