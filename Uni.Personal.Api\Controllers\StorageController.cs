using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using UNI.Model;
using UNI.Model.Api;
using UNI.Utilities.ExternalStorage.Abstractions;
using UNI.Utilities.ExternalStorage.Models;

namespace Uni.Personal.Api.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class StorageController : UniController
    {
        private readonly IStorageService _storageService;
        private readonly ILogger<StorageController> _logger;

        public StorageController(IStorageService storageService, ILogger<StorageController> logger)
        {
            _storageService = storageService;
            _logger = logger;
        }

        [HttpGet]
        [Route("GetFile")]
        public async Task<IActionResult> GetFile(string path, string action = "default", ImageSizeEnum? size = null)
        {
            if (path == null) return new BadRequestObjectResult("path not found");
            if (path.StartsWith("http"))
            {
                return action == "url" ? Ok(path) : Redirect(path);
            }

            if (path.StartsWith("minio://") == false)
                return new BadRequestObjectResult("Scheme not supported");

            try
            {
                // Parse minio:// path to extract bucket and object name
                // Format: minio://bucket/object/path
                var uri = new Uri(path);
                var bucketName = uri.Host;
                var objectName = uri.AbsolutePath.TrimStart('/');

                if (string.IsNullOrEmpty(bucketName) || string.IsNullOrEmpty(objectName))
                {
                    return new BadRequestObjectResult("Invalid minio path format");
                }

                // Get preview URL for the object
                var url = await _storageService.GetPreviewUrlAsync(bucketName, objectName);

                // Create result object with Url property
                var result = new { Url = url };

                return action switch
                {
                    "url" => Ok(result.Url),
                    "info" => Ok(GetResponse(ApiResult.Success, result)),
                    _ => Redirect(result.Url)
                };
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "File not found or error accessing file {File}", path);
                return new NotFoundObjectResult("File not found");
            }
        }
        [HttpPost]
        [Route("UploadFile")]
        public async Task<BaseResponse<UploadResponse>> UploadFile(IFormFile file, string bucketName, string? objectName = null)
        {
            try
            {
                if (file == null || file.Length == 0)
                {
                    return GetResponse<UploadResponse>(ApiResult.Fail, null!, "No file provided");
                }

                // Use the original filename if objectName is not provided
                var finalObjectName = objectName ?? file.FileName;

                // Upload using stream from IFormFile
                using var stream = file.OpenReadStream();
                var result = await _storageService.UploadObjectAsync(
                    bucketName,
                    finalObjectName,
                    stream,
                    file.Length,
                    file.ContentType);

                // Convert StorageUploadResult to UploadResponse
                var uploadResponse = new UploadResponse
                {
                    Size = result.Size,
                    ObjectName = result.ObjectName,
                    Bucket = result.BucketName,
                    FilePath = $"minio://{result.BucketName}/{result.ObjectName}",
                    FileName = file.FileName,
                    ContentType = result.ContentType ?? file.ContentType,
                    Url = await _storageService.GetPreviewUrlAsync(result.BucketName, result.ObjectName),
                    UrlExpiration = 0
                };

                return GetResponse(ApiResult.Success, uploadResponse);
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Error uploading file {FileName}", file?.FileName);
                return GetResponse<UploadResponse>(ApiResult.Fail, null!, e.Message);
            }
        }


    }
}