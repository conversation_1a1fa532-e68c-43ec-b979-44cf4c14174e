using System;

namespace Uni.Personal.Model
{
    /// <summary>
    /// Order item view model for displaying order line items
    /// </summary>
    public class OrderItemView
    {
        public Guid Oid { get; set; }
        public Guid OrderOid { get; set; }
        public int ItemNo { get; set; }
        public string? ProductType { get; set; }
        public string? ProductName { get; set; }
        public string? ProductDescription { get; set; }
        public int Quantity { get; set; }
        public string? Unit { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal TotalPrice { get; set; }
        public int? Duration { get; set; }
        public string? DurationUnit { get; set; }
    }
}
