
using System;
using System.Threading.Tasks;
using UNI.Common.CommonBase;
using UNI.Model;
using Uni.Personal.BLL.Interfaces;
using Uni.Personal.DAL.Interfaces;
using Uni.Personal.Model;
using Uni.Personal.DAL.Repositories;

namespace Uni.Personal.BLL.Services
    {
        /// <summary>
        /// Order service implementation
        /// </summary>
        public class TicketService : ITicketService
    {
            private readonly ITicketRepository _ticketRepository;

            public TicketService(ITicketRepository ticketRepository)
            {
                _ticketRepository = ticketRepository;
            }

            /// <summary>
            /// Get paginated list of orders
            /// </summary>
            /// <param name="query">Filter input for orders</param>
            /// <returns>Paginated list of orders</returns>
            public async Task<CommonListPage> GetPageAsync(TicketFilterInput? query)
            {
                return await _ticketRepository.GetPageAsync(query);
            }

            /// <summary>
            /// Get order information by ID
            /// </summary>
            /// <param name="oid">Order ID</param>
            /// <returns>Order information</returns>
            public async Task<CommonViewOidInfo> GetInfoAsync(Guid? oid)
            {
                return await _ticketRepository.GetInfoAsync(oid);
            }

            /// <summary>
            /// Create or update order
            /// </summary>
            /// <param name="info">Order information</param>
            /// <returns>Validation result with order ID</returns>
            public async Task<BaseValidate<Guid?>> SetInfoAsync(CommonViewOidInfo info)
            {
                return await _ticketRepository.SetInfoAsync(info);
            }

            /// <summary>
            /// Delete order
            /// </summary>
            /// <param name="oid">Order ID</param>
            /// <returns>Validation result</returns>
            public async Task<BaseValidate> DeleteAsync(Guid? oid)
            {
                // Add business logic validation here if needed
                // For example: check if order can be deleted, validate permissions, etc.

                return await _ticketRepository.DeleteAsync(oid);
            }

        }
    }
