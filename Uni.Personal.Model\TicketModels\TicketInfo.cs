using UNI.Model;

namespace Uni.Personal.Model
{
    /// <summary>
    /// Order information model for create/update operations
    /// </summary>
    public class TicketInfo : CommonViewOidInfo
    {
        public Guid? oid { get; set; }
        public string? ticket_no { get; set; }
        public int? request_type_id { get; set; }
        public Guid? customer_id { get; set; }
        public string? subject { get; set; }
        public string? content { get; set; }
        public Guid? assigned_to { get; set; }
        public int? priority_level { get; set; }
        public int? status { get; set; }
        public string? resolution_note { get; set; }
        public int? source_type { get; set; }
        public DateTime? created_date { get; set; }
        public string? created_by { get; set; }
    }
}
