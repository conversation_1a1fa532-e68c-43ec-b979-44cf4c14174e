using UNI.Model;
using Uni.Personal.Model;

namespace Uni.Personal.BLL.Interfaces
{
    /// <summary>
    /// Contract service interface
    /// </summary>
    public interface IContractService
    {
        /// <summary>
        /// Get paginated list of contracts
        /// </summary>
        /// <param name="query">Filter input for contracts</param>
        /// <returns>Paginated list of contracts</returns>
        Task<CommonListPage> GetPageAsync(ContractFilterInput? query);
    }
}
