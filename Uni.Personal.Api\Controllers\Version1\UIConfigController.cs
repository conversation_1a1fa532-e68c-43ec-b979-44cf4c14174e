using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using UNI.Model;
using UNI.Model.Api;
using Uni.Personal.BLL.Interfaces;

namespace Uni.Personal.Api.Controllers.Version1
{
    [Route("/api/v1/[controller]/[action]")]
    [ApiController]
    [Authorize]
    public class UIConfigController : UniController
    {
        private readonly IUIConfigService _uiConfigService;

        public UIConfigController(IUIConfigService uiConfigService)
        {
            _uiConfigService = uiConfigService;
        }

        /// <summary>
        /// Get configuration language filter
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="acceptLanguage">Accept language</param>
        /// <returns>Configuration language filter</returns>
        [HttpGet]
        public async Task<BaseResponse<CommonViewInfo>> GetConfigLanguageFilter([FromQuery] string userId, [FromQuery] string acceptLanguage)
        {
            if (string.IsNullOrEmpty(userId) || string.IsNullOrEmpty(acceptLanguage))
            {
                return GetErrorResponse<CommonViewInfo>(ApiResult.Error, 12, "User ID and Accept Language are required");
            }
            var result = await _uiConfigService.GetConfigLanguageFilter(userId, acceptLanguage);
            return GetResponse(ApiResult.Success, result);
        }

        /// <summary>
        /// Get form view page
        /// </summary>
        /// <param name="filter">Table key filter</param>
        /// <returns>Form view page</returns>
        [HttpGet]
        public async Task<BaseResponse<CommonListPage>> GetFormViewPage([FromQuery] FilterInpTableKey? filter)
        {
            if (filter == null)
            {
                return GetErrorResponse<CommonListPage>(ApiResult.Error, 12, "Filter is required");
            }
            var result = await _uiConfigService.GetFormViewPage(filter);
            return GetResponse(ApiResult.Success, result);
        }

        /// <summary>
        /// Set form view information
        /// </summary>
        /// <param name="para">Configuration field</param>
        /// <returns>Validation result</returns>
        [HttpPost]
        public async Task<BaseResponse<BaseValidate>> SetFormViewInfo([FromBody] ConfigField? para)
        {
            if (para == null)
            {
                return GetErrorResponse<BaseValidate>(ApiResult.Error, 12, "Configuration field is required");
            }

            var result = await _uiConfigService.SetFormViewInfo(para);
            return result.valid ? GetResponse(ApiResult.Success, result) : GetResponse(ApiResult.Error, result, result.messages);
        }

        /// <summary>
        /// Delete form view information
        /// </summary>
        /// <param name="id">Form view ID</param>
        /// <returns>Validation result</returns>
        [HttpDelete]
        public async Task<BaseResponse<BaseValidate>> DelFormViewInfo([FromQuery] long id)
        {
            var result = await _uiConfigService.DelFormViewInfo(id);
            return result.valid ? GetResponse(ApiResult.Success, result) : GetResponse(ApiResult.Error, result, result.messages);
        }

        /// <summary>
        /// Get grid view filter
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="acceptLanguage">Accept language</param>
        /// <returns>Grid view filter</returns>
        [HttpGet]
        public async Task<BaseResponse<CommonViewInfo>> GetGridViewFilter([FromQuery] string userId, [FromQuery] string acceptLanguage)
        {
            if (string.IsNullOrEmpty(userId) || string.IsNullOrEmpty(acceptLanguage))
            {
                return GetErrorResponse<CommonViewInfo>(ApiResult.Error, 12, "User ID and Accept Language are required");
            }
            var result = await _uiConfigService.GetGridViewFilter(userId, acceptLanguage);
            return GetResponse(ApiResult.Success, result);
        }

        /// <summary>
        /// Get grid view page
        /// </summary>
        /// <param name="filter">Grid key filter</param>
        /// <returns>Grid view page</returns>
        [HttpGet]
        public async Task<BaseResponse<CommonListPage>> GetGridViewPage([FromQuery] FilterInpGridKey? filter)
        {
            if (filter == null)
            {
                return GetErrorResponse<CommonListPage>(ApiResult.Error, 12, "Filter is required");
            }
            var result = await _uiConfigService.GetGridViewPage(filter);
            return GetResponse(ApiResult.Success, result);
        }

        /// <summary>
        /// Set grid view information
        /// </summary>
        /// <param name="para">Configuration column</param>
        /// <returns>Validation result</returns>
        [HttpPost]
        public async Task<BaseResponse<BaseValidate>> SetGridViewInfo([FromBody] ConfigColumn? para)
        {
            if (para == null)
            {
                return GetErrorResponse<BaseValidate>(ApiResult.Error, 12, "Configuration column is required");
            }

            var result = await _uiConfigService.SetGridViewInfo(para);
            return result.valid ? GetResponse(ApiResult.Success, result) : GetResponse(ApiResult.Error, result, result.messages);
        }

        /// <summary>
        /// Delete grid view information
        /// </summary>
        /// <param name="gridId">Grid ID</param>
        /// <returns>Validation result</returns>
        [HttpDelete]
        public async Task<BaseResponse<BaseValidate>> DelGridViewInfo([FromQuery] long gridId)
        {
            var result = await _uiConfigService.DelGridViewInfo(gridId);
            return result.valid ? GetResponse(ApiResult.Success, result) : GetResponse(ApiResult.Error, result, result.messages);
        }

        /// <summary>
        /// Get group information
        /// </summary>
        /// <param name="key_1">Key 1</param>
        /// <param name="key_2">Key 2</param>
        /// <returns>Group information</returns>
        [HttpGet]
        public async Task<BaseResponse<CommonViewInfo>> GetGroupInfo([FromQuery] string key_1, [FromQuery] string key_2)
        {
            if (string.IsNullOrEmpty(key_1) || string.IsNullOrEmpty(key_2))
            {
                return GetErrorResponse<CommonViewInfo>(ApiResult.Error, 12, "Key 1 and Key 2 are required");
            }
            var result = await _uiConfigService.GetGroupInfo(key_1, key_2);
            return GetResponse(ApiResult.Success, result);
        }

        /// <summary>
        /// Set group information
        /// </summary>
        /// <param name="para">Common view information</param>
        /// <returns>Validation result</returns>
        [HttpPost]
        public async Task<BaseResponse<BaseValidate>> SetGroupInfo([FromBody] CommonViewInfo? para)
        {
            if (para == null)
            {
                return GetErrorResponse<BaseValidate>(ApiResult.Error, 12, "Group information is required");
            }

            var result = await _uiConfigService.SetGroupInfo(para);
            return result.valid ? GetResponse(ApiResult.Success, result) : GetResponse(ApiResult.Error, result, result.messages);
        }

        /// <summary>
        /// Set filter information
        /// </summary>
        /// <param name="gridKey">Grid key</param>
        /// <param name="para">Filter parameters</param>
        /// <returns>Success response</returns>
        [HttpPost]
        public async Task<BaseResponse<object>> SetFilterInfo([FromQuery] string gridKey, [FromBody] Dictionary<string, string>? para)
        {
            if (string.IsNullOrEmpty(gridKey) || para == null)
            {
                return GetErrorResponse<object>(ApiResult.Error, 12, "Grid key and filter parameters are required");
            }

            await _uiConfigService.SetFilterInfo(gridKey, para);
            return GetResponse(ApiResult.Success, new object());
        }
    }
}
