using System;
using System.IO;
using System.Threading;
using System.Threading.Tasks;

namespace UNI.Utilities.MinIo.Internal
{
    /// <summary>
    /// A stream wrapper that tracks upload progress and reports it via IProgress callback
    /// </summary>
    internal class ProgressTrackingStream : Stream
    {
        private readonly Stream _baseStream;
        private readonly long _totalSize;
        private readonly IProgress<UploadProgress>? _progressCallback;
        private readonly DateTime _startTime;
        private long _bytesRead;
        private DateTime _lastProgressUpdate;

        public ProgressTrackingStream(Stream baseStream, long totalSize, IProgress<UploadProgress>? progressCallback, DateTime startTime)
        {
            _baseStream = baseStream ?? throw new ArgumentNullException(nameof(baseStream));
            _totalSize = totalSize;
            _progressCallback = progressCallback;
            _startTime = startTime;
            _lastProgressUpdate = startTime;
        }

        public override bool CanRead => _baseStream.CanRead;
        public override bool CanSeek => _baseStream.CanSeek;
        public override bool CanWrite => _baseStream.CanWrite;
        public override long Length => _baseStream.Length;

        public override long Position
        {
            get => _baseStream.Position;
            set => _baseStream.Position = value;
        }

        public override void Flush() => _baseStream.Flush();

        public override int Read(byte[] buffer, int offset, int count)
        {
            var bytesRead = _baseStream.Read(buffer, offset, count);
            UpdateProgress(bytesRead);
            return bytesRead;
        }

        public override async Task<int> ReadAsync(byte[] buffer, int offset, int count, CancellationToken cancellationToken)
        {
            var bytesRead = await _baseStream.ReadAsync(buffer, offset, count, cancellationToken);
            UpdateProgress(bytesRead);
            return bytesRead;
        }

        public override long Seek(long offset, SeekOrigin origin) => _baseStream.Seek(offset, origin);

        public override void SetLength(long value) => _baseStream.SetLength(value);

        public override void Write(byte[] buffer, int offset, int count)
        {
            _baseStream.Write(buffer, offset, count);
            UpdateProgress(count);
        }

        public override async Task WriteAsync(byte[] buffer, int offset, int count, CancellationToken cancellationToken)
        {
            await _baseStream.WriteAsync(buffer, offset, count, cancellationToken);
            UpdateProgress(count);
        }

        private void UpdateProgress(int bytesProcessed)
        {
            if (_progressCallback == null) return;

            _bytesRead += bytesProcessed;
            var now = DateTime.UtcNow;

            // Update progress every 100ms or when significant progress is made
            if (now - _lastProgressUpdate > TimeSpan.FromMilliseconds(100) || 
                _bytesRead >= _totalSize)
            {
                var elapsed = now - _startTime;
                var bytesPerSecond = elapsed.TotalSeconds > 0 ? (long)(_bytesRead / elapsed.TotalSeconds) : 0;
                
                TimeSpan? estimatedTimeRemaining = null;
                if (bytesPerSecond > 0 && _bytesRead < _totalSize)
                {
                    var remainingBytes = _totalSize - _bytesRead;
                    estimatedTimeRemaining = TimeSpan.FromSeconds(remainingBytes / (double)bytesPerSecond);
                }

                var progress = new UploadProgress
                {
                    TotalBytes = _totalSize,
                    UploadedBytes = _bytesRead,
                    BytesPerSecond = bytesPerSecond,
                    EstimatedTimeRemaining = estimatedTimeRemaining,
                    IsMultipartUpload = true,
                    Status = _bytesRead >= _totalSize ? "Upload completed" : "Uploading"
                };

                _progressCallback.Report(progress);
                _lastProgressUpdate = now;
            }
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                // Report final progress
                if (_progressCallback != null)
                {
                    var finalProgress = new UploadProgress
                    {
                        TotalBytes = _totalSize,
                        UploadedBytes = _bytesRead,
                        IsMultipartUpload = true,
                        Status = "Upload completed"
                    };
                    _progressCallback.Report(finalProgress);
                }
            }
            base.Dispose(disposing);
        }
    }
}
