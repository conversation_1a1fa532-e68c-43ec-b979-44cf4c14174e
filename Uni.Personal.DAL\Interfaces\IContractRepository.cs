using System.Threading.Tasks;
using UNI.Model;
using Uni.Personal.Model;

namespace Uni.Personal.DAL.Interfaces
{
    /// <summary>
    /// Contract repository interface
    /// </summary>
    public interface IContractRepository
    {
        /// <summary>
        /// Get paginated list of contracts
        /// </summary>
        /// <param name="filter">Filter criteria</param>
        /// <returns>Paginated list of contracts</returns>
        Task<CommonListPage> GetPageAsync(ContractFilterInput? filter);
    }
}
