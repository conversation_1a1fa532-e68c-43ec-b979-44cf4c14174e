using UNI.Model;

namespace Uni.Personal.Model
{
    /// <summary>
    /// Filter input for Order pagination
    /// </summary>
    public class OrderFilterInput : FilterInput
    {
        public string? OrderCode { get; set; }
        public string? CustomerName { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public int? Status { get; set; }
        public string? ProductType { get; set; }
    }
}
