using UNI.Model;

namespace Uni.Personal.Model
{
    /// <summary>
    /// Contract information model for create/update operations
    /// </summary>
    public class ContractInfo : CommonViewOidInfo
    {
        public Guid? OrderId { get; set; }
        public Guid? OrderDetailsId { get; set; }
        public string? ContractNo { get; set; }
        public DateTime? ContractDate { get; set; }
        public int? Status { get; set; }
        public string? ContractLink { get; set; }
        public string? OrderName { get; set; }
        public string? CustomerName { get; set; }
    }
}
