using UNI.Utilities.ExternalStorage.Models;

namespace UNI.Utilities.ExternalStorage.Exceptions
{
    /// <summary>
    /// Base exception for storage operations
    /// </summary>
    public class StorageException : Exception
    {
        /// <summary>
        /// Storage provider type where the exception occurred
        /// </summary>
        public StorageProviderType? ProviderType { get; }

        /// <summary>
        /// Error code from the storage provider
        /// </summary>
        public string? ErrorCode { get; }

        /// <summary>
        /// Additional error details
        /// </summary>
        public Dictionary<string, object> ErrorDetails { get; }

        /// <summary>
        /// Constructor for StorageException
        /// </summary>
        /// <param name="message">Error message</param>
        /// <param name="providerType">Storage provider type</param>
        /// <param name="errorCode">Error code</param>
        /// <param name="innerException">Inner exception</param>
        public StorageException(
            string message, 
            StorageProviderType? providerType = null, 
            string? errorCode = null, 
            Exception? innerException = null)
            : base(message, innerException)
        {
            ProviderType = providerType;
            ErrorCode = errorCode;
            ErrorDetails = new Dictionary<string, object>();
        }

        /// <summary>
        /// Constructor for StorageException with error details
        /// </summary>
        /// <param name="message">Error message</param>
        /// <param name="providerType">Storage provider type</param>
        /// <param name="errorCode">Error code</param>
        /// <param name="errorDetails">Additional error details</param>
        /// <param name="innerException">Inner exception</param>
        public StorageException(
            string message, 
            StorageProviderType? providerType, 
            string? errorCode, 
            Dictionary<string, object> errorDetails, 
            Exception? innerException = null)
            : base(message, innerException)
        {
            ProviderType = providerType;
            ErrorCode = errorCode;
            ErrorDetails = errorDetails ?? new Dictionary<string, object>();
        }
    }

    /// <summary>
    /// Exception thrown when a bucket/container is not found
    /// </summary>
    public class BucketNotFoundException : StorageException
    {
        /// <summary>
        /// Name of the bucket that was not found
        /// </summary>
        public string BucketName { get; }

        /// <summary>
        /// Constructor for BucketNotFoundException
        /// </summary>
        /// <param name="bucketName">Name of the bucket</param>
        /// <param name="providerType">Storage provider type</param>
        /// <param name="innerException">Inner exception</param>
        public BucketNotFoundException(
            string bucketName, 
            StorageProviderType? providerType = null, 
            Exception? innerException = null)
            : base($"Bucket '{bucketName}' not found", providerType, "BucketNotFound", innerException)
        {
            BucketName = bucketName;
        }
    }

    /// <summary>
    /// Exception thrown when an object is not found
    /// </summary>
    public class ObjectNotFoundException : StorageException
    {
        /// <summary>
        /// Name of the bucket
        /// </summary>
        public string BucketName { get; }

        /// <summary>
        /// Name of the object that was not found
        /// </summary>
        public string ObjectName { get; }

        /// <summary>
        /// Constructor for ObjectNotFoundException
        /// </summary>
        /// <param name="bucketName">Name of the bucket</param>
        /// <param name="objectName">Name of the object</param>
        /// <param name="providerType">Storage provider type</param>
        /// <param name="innerException">Inner exception</param>
        public ObjectNotFoundException(
            string bucketName, 
            string objectName, 
            StorageProviderType? providerType = null, 
            Exception? innerException = null)
            : base($"Object '{objectName}' not found in bucket '{bucketName}'", providerType, "ObjectNotFound", innerException)
        {
            BucketName = bucketName;
            ObjectName = objectName;
        }
    }

    /// <summary>
    /// Exception thrown when access is denied
    /// </summary>
    public class StorageAccessDeniedException : StorageException
    {
        /// <summary>
        /// Constructor for StorageAccessDeniedException
        /// </summary>
        /// <param name="message">Error message</param>
        /// <param name="providerType">Storage provider type</param>
        /// <param name="innerException">Inner exception</param>
        public StorageAccessDeniedException(
            string message, 
            StorageProviderType? providerType = null, 
            Exception? innerException = null)
            : base(message, providerType, "AccessDenied", innerException)
        {
        }
    }

    /// <summary>
    /// Exception thrown when authentication fails
    /// </summary>
    public class StorageAuthenticationException : StorageException
    {
        /// <summary>
        /// Constructor for StorageAuthenticationException
        /// </summary>
        /// <param name="message">Error message</param>
        /// <param name="providerType">Storage provider type</param>
        /// <param name="innerException">Inner exception</param>
        public StorageAuthenticationException(
            string message, 
            StorageProviderType? providerType = null, 
            Exception? innerException = null)
            : base(message, providerType, "AuthenticationFailed", innerException)
        {
        }
    }

    /// <summary>
    /// Exception thrown when a storage operation times out
    /// </summary>
    public class StorageTimeoutException : StorageException
    {
        /// <summary>
        /// Timeout duration
        /// </summary>
        public TimeSpan Timeout { get; }

        /// <summary>
        /// Constructor for StorageTimeoutException
        /// </summary>
        /// <param name="timeout">Timeout duration</param>
        /// <param name="providerType">Storage provider type</param>
        /// <param name="innerException">Inner exception</param>
        public StorageTimeoutException(
            TimeSpan timeout, 
            StorageProviderType? providerType = null, 
            Exception? innerException = null)
            : base($"Storage operation timed out after {timeout.TotalSeconds} seconds", providerType, "Timeout", innerException)
        {
            Timeout = timeout;
        }
    }

    /// <summary>
    /// Exception thrown when storage quota is exceeded
    /// </summary>
    public class StorageQuotaExceededException : StorageException
    {
        /// <summary>
        /// Constructor for StorageQuotaExceededException
        /// </summary>
        /// <param name="message">Error message</param>
        /// <param name="providerType">Storage provider type</param>
        /// <param name="innerException">Inner exception</param>
        public StorageQuotaExceededException(
            string message, 
            StorageProviderType? providerType = null, 
            Exception? innerException = null)
            : base(message, providerType, "QuotaExceeded", innerException)
        {
        }
    }

    /// <summary>
    /// Exception thrown when storage configuration is invalid
    /// </summary>
    public class StorageConfigurationException : StorageException
    {
        /// <summary>
        /// Constructor for StorageConfigurationException
        /// </summary>
        /// <param name="message">Error message</param>
        /// <param name="providerType">Storage provider type</param>
        /// <param name="innerException">Inner exception</param>
        public StorageConfigurationException(
            string message, 
            StorageProviderType? providerType = null, 
            Exception? innerException = null)
            : base(message, providerType, "ConfigurationError", innerException)
        {
        }
    }
}
