using UNI.Model;

namespace Uni.Personal.Model
{
    /// <summary>
    /// Filter input for Contract pagination
    /// </summary>
    public class ContractFilterInput : FilterInput
    {
        public string? ContractNo { get; set; }
        public string? OrderCode { get; set; }
        public string? CustomerName { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public int? Status { get; set; }
    }
}
