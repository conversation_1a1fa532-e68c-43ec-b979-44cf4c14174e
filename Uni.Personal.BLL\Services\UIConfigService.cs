using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using UNI.Model;
using Uni.Personal.BLL.Interfaces;
using Uni.Personal.DAL.Interfaces;

namespace Uni.Personal.BLL.Services
{
    /// <summary>
    /// UIConfig service implementation
    /// </summary>
    public class UIConfigService : IUIConfigService
    {
        private readonly IUIConfigRepository _uiConfigRepository;

        public UIConfigService(IUIConfigRepository uiConfigRepository)
        {
            _uiConfigRepository = uiConfigRepository;
        }

        /// <summary>
        /// Get configuration language filter
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="acceptLanguage">Accept language</param>
        /// <returns>Configuration language filter</returns>
        public async Task<CommonViewInfo> GetConfigLanguageFilter(string userId, string acceptLanguage)
        {
            return await _uiConfigRepository.GetConfigLanguageFilter(userId, acceptLanguage);
        }

        /// <summary>
        /// Get form view page
        /// </summary>
        /// <param name="filter">Table key filter</param>
        /// <returns>Form view page</returns>
        public async Task<CommonListPage> GetFormViewPage(FilterInpTableKey filter)
        {
            return await _uiConfigRepository.GetFormViewPage(filter);
        }

        /// <summary>
        /// Set form view information
        /// </summary>
        /// <param name="para">Configuration field</param>
        /// <returns>Validation result</returns>
        public async Task<BaseValidate> SetFormViewInfo(ConfigField para)
        {
            // Add business logic validation here if needed
            return await _uiConfigRepository.SetFormViewInfo(para);
        }

        /// <summary>
        /// Delete form view information
        /// </summary>
        /// <param name="id">Form view ID</param>
        /// <returns>Validation result</returns>
        public async Task<BaseValidate> DelFormViewInfo(long id)
        {
            // Add business logic validation here if needed
            return await _uiConfigRepository.DelFormViewInfo(id);
        }

        /// <summary>
        /// Get grid view filter
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="acceptLanguage">Accept language</param>
        /// <returns>Grid view filter</returns>
        public async Task<CommonViewInfo> GetGridViewFilter(string userId, string acceptLanguage)
        {
            return await _uiConfigRepository.GetGridViewFilter(userId, acceptLanguage);
        }

        /// <summary>
        /// Get grid view page
        /// </summary>
        /// <param name="filter">Grid key filter</param>
        /// <returns>Grid view page</returns>
        public async Task<CommonListPage> GetGridViewPage(FilterInpGridKey filter)
        {
            return await _uiConfigRepository.GetGridViewPage(filter);
        }

        /// <summary>
        /// Set grid view information
        /// </summary>
        /// <param name="para">Configuration column</param>
        /// <returns>Validation result</returns>
        public async Task<BaseValidate> SetGridViewInfo(ConfigColumn para)
        {
            // Add business logic validation here if needed
            return await _uiConfigRepository.SetGridViewInfo(para);
        }

        /// <summary>
        /// Delete grid view information
        /// </summary>
        /// <param name="gridId">Grid ID</param>
        /// <returns>Validation result</returns>
        public async Task<BaseValidate> DelGridViewInfo(long gridId)
        {
            // Add business logic validation here if needed
            return await _uiConfigRepository.DelGridViewInfo(gridId);
        }

        /// <summary>
        /// Get group information
        /// </summary>
        /// <param name="key_1">Key 1</param>
        /// <param name="key_2">Key 2</param>
        /// <returns>Group information</returns>
        public async Task<CommonViewInfo> GetGroupInfo(string key_1, string key_2)
        {
            return await _uiConfigRepository.GetGroupInfo(key_1, key_2);
        }

        /// <summary>
        /// Set group information
        /// </summary>
        /// <param name="para">Common view information</param>
        /// <returns>Validation result</returns>
        public async Task<BaseValidate> SetGroupInfo(CommonViewInfo para)
        {
            // Add business logic validation here if needed
            return await _uiConfigRepository.SetGroupInfo(para);
        }

        /// <summary>
        /// Set filter information
        /// </summary>
        /// <param name="gridKey">Grid key</param>
        /// <param name="para">Filter parameters</param>
        /// <returns>Task</returns>
        public async Task SetFilterInfo(string gridKey, Dictionary<string, string> para)
        {
            await _uiConfigRepository.SetFilterInfo(gridKey, para);
        }
    }
}
