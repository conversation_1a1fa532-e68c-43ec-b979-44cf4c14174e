using System;
using System.Threading.Tasks;
using UNI.Common.CommonBase;
using UNI.Model;
using Uni.Personal.Model;

namespace Uni.Personal.DAL.Interfaces
{
    /// <summary>
    /// Order repository interface
    /// </summary>
    public interface ITicketRepository
    {
        // Inherits all methods from IBaseRepository:
        Task<CommonListPage> GetPageAsync(TicketFilterInput? filter);
        Task<CommonViewOidInfo> GetInfoAsync(Guid? oid);
        Task<BaseValidate<Guid?>> SetInfoAsync(CommonViewOidInfo info);
        Task<BaseValidate> DeleteAsync(Guid? oid);

    }
}
