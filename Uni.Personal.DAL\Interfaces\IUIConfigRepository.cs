using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using UNI.Model;

namespace Uni.Personal.DAL.Interfaces
{
    public interface IUIConfigRepository
    {

        Task<CommonViewInfo> GetConfigLanguageFilter(string userId, string acceptLanguage);

        Task<CommonListPage> GetFormViewPage(FilterInpTableKey filter);
        Task<BaseValidate> SetFormViewInfo(ConfigField para);
        Task<BaseValidate> DelFormViewInfo(long id);
        Task<CommonViewInfo> GetGridViewFilter(string userId, string acceptLanguage);
        Task<CommonListPage> GetGridViewPage(FilterInpGridKey filter);
        Task<BaseValidate> SetGridViewInfo(ConfigColumn para);
        Task<BaseValidate> DelGridViewInfo(long gridId);

        Task<CommonViewInfo> GetGroupInfo(string key_1, string key_2);
        Task<BaseValidate> SetGroupInfo(CommonViewInfo para);
        Task SetFilterInfo(string gridKey, Dictionary<string, string> para);
        //DataSet GetFieldTranslate_Download(bool isFull, string table_name, string acceptLanguage);
        // DataSet GetFieldTranslate_Download(bzzUiConfigFilter query, string acceptLanguage);
        //Task<ImportListPage> SetRecordImport(string userId, string acceptLanguage, bzzFieldTranslateImportSet workShiftRecords, int serial_is, string roleToken);

    }
}